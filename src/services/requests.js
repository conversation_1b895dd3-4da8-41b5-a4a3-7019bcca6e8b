import {
  transform_measurement_svg,
  transform_fancy_stone_measurement_svg,
} from '../utils/transform_measurement_svg.js';
import fs from 'fs/promises';
import dotenv from 'dotenv';
import path from 'path';
import {
  generate_presigned_url,
} from '../utils/s3_utils.js';

const girdle_size_mappings = {
  ETK: 'EXTREMELY_THICK',
  MED: 'MEDIUM',
  STK: 'SLIGHTLY_THICK',
  THK: 'THICK',
  THN: 'THIN',
  VTK: 'VERY_THICK',
  VTN: 'VERY_THIN',
  ETN: 'EXTREMELY_THIN',
};

const culet_mappings = {
  NON: 'NONE',
  ELG: 'EXTREMELY_LARGE',
  LGE: 'LARGE',
  MED: 'MEDIUM',
  SLG: 'SLIGHTLY_LARGE',
  SML: 'SMALL',
  VLG: 'VERY_LARGE',
};

const files_to_presign = {
  'internal-crown-plotting': 'internal_crown_plotting.svg',
  'external-crown-plotting': 'external_crown_plotting.svg',
  'internal-pavilion-plotting': 'internal_pavilion_plotting.svg',
  'external-pavilion-plotting': 'external_pavilion_plotting.svg',
  'internal-comments': 'internal_comments.txt',
  'external-comments': 'external_comments.txt',
};

export const get_stone_measurements = async(req, res) => {
  try {
    let request_no = req?.params?.request_no;
    let is_fancy_shape = req?.query?.is_fancy_shape === 'true';
    if (!request_no) {
      throw new Error('Please provide valid request number.');
    }
    let file_content = await fs.readFile(path.join(process.env.COMMON_STORAGE_PATH, request_no, `${request_no}.txt`));
    let parsed_content = dotenv.parse(file_content);
    if (!parsed_content) {
      throw new Error(`Error in parsing the measurement text file for ${request_no}`);
    }
    let stone_measurements = {
      length: !is_fancy_shape ? (parseFloat(parsed_content.DIAMETER_MM_MAX).toFixed(2) ?? null)
        : (parseFloat(parsed_content.LENGTH_MM).toFixed(2) ?? null),
      width: !is_fancy_shape ? (parseFloat(parsed_content.DIAMETER_MM_MIN).toFixed(2) ?? null)
        : (parseFloat(parsed_content.WIDTH_MM).toFixed(2) ?? null),
      depth: parseFloat(parsed_content.TOTAL_DEPTH_MM).toFixed(2) ?? null,
      depth_percentage: parsed_content.TOTAL_DEPTH_PC ?? null,
      table: parsed_content.TABLE_PC ?? null,
      crown_angle: parsed_content.CROWN_ANGLE_DEG ?? null,
      crown_percent: parsed_content.CROWN_HEIGHT_GIA_ROUNDED ?? null,
      crown_height: parsed_content.CROWN_HEIGHT_PC ?? null,
      pav_angle: parsed_content.PAVILION_ANGLE_DEG ?? null,
      pav_percent: !is_fancy_shape ? (parsed_content.PAVILION_DEPTH_GIA_ROUNDED ?? null) : null,
      pav_depth: parsed_content.PAVILION_HEIGHT_PC_AVG ?? null,
      girdle_percent: parsed_content.GIRDLE_WIDE_BEZEL ?? null,
      // not having condition on fancy shape below because as per Lexus team the value should be
      // picked from GIRDLE_NARROW_MIN & GIRDLE_NARROW_MAX. But these fields are absent in fancy shape.
      girdle_min_percent: parsed_content.GIRDLE_NARROW_MIN ?? parsed_content.GIRDLE_THICKNESS_PC_MIN ?? null,
      girdle_max_percent: parsed_content.GIRDLE_NARROW_MAX ?? parsed_content.GIRDLE_THICKNESS_PC_MAX ?? null,
      culet_size_percent: parsed_content.CULET_PC ?? null,
      table_off_center_percent: parsed_content.OFFSET_GIRDLE_TABLE_PC ?? null,
      x_table_off_center_percent: is_fancy_shape ? (parsed_content.OFFSETX_GIRDLE_TABLE_PC ?? null)
        : null,
      y_table_off_center_percent: is_fancy_shape ? (parsed_content.OFFSETY_GIRDLE_TABLE_PC ?? null)
        : null,
      x_culet_off_center_percent: is_fancy_shape ? (parsed_content.OFFSETX_GIRDLE_CULET_PC ?? null)
        : null,
      y_culet_off_center_percent: is_fancy_shape ? (parsed_content.OFFSETY_GIRDLE_CULET_PC ?? null)
        : null,
      culet_off_center_percent: parsed_content.OFFSETX_GIRDLE_CULET_PC ?? null,
      girdle_min: girdle_size_mappings[parsed_content.GIRDLE_THICKNESS_MIN_GIA_ROUNDED] || 'NULL',
      girdle_max: girdle_size_mappings[parsed_content.GIRDLE_THICKNESS_MAX_GIA_ROUNDED] || 'NULL',
      culet_size: culet_mappings[parsed_content.CULET_SIZE_GIA_ROUNDED] || '',
      cut_grade: parsed_content.OVERALL_FINAL_GRADE ?? null,
      symmetry_grade: parsed_content.OVERALL_SYM ?? null,
      star_length_percent: parsed_content.STAR_RATIO_PC ?? parsed_content.STAR_RATIO_PC ?? null,
      lower_girdle_length_percent: parsed_content.LENGTH_GIRDLE_FACET ?? parsed_content.LENGTH_GIRDLE_FACET ?? null,
    };
    res.success({
      data: stone_measurements,
      status_code: 200,
    });
  } catch (err) {
    console.error(`Error in getting measurements for request id - ${req?.query?.request_id}`, err);
    res.error({
      errors: err,
      status_code: 500,
    })
  }
};

export const get_stone_measurement_diagram = async(req, res) => {
  try {
    let request_no = req?.params?.request_no;
    if (!request_no) {
      throw new Error('Please provide valid request no.');
    }
    let is_fancy_shape = req?.query?.is_fancy_shape === 'true'
      ? true
      : false;
    let updated_diagram;
    if (is_fancy_shape) {
      updated_diagram = await transform_fancy_stone_measurement_svg(request_no);
    } else {
      updated_diagram = await transform_measurement_svg(request_no);
    }
    res.setHeader('Content-type', 'image/svg+xml');
    res.status(200).send(updated_diagram);
    return;
  } catch (err) {
    console.error(`Error in getting stone measurement diagram`, err);
    res.error({
      errors: err,
      status_code: 500,
    });
  }
};

export const get_stone_plotting_files = async(req, res) => {
  try {
    let request_no = req?.params?.request_no;
    if (!request_no) {
      throw new Error('Please provide valid request no.');
    }
    let file_type = req?.params?.file_type;
    if (!file_type) {
      throw new Error('Please provide valid file type.');
    }
    let file_path = path.join(process.env.COMMON_STORAGE_PATH, request_no);
    switch (file_type) {
      case 'internal-crown-plotting':
        file_path = path.join(file_path, 'Internal', `Internal_CROWN.svg`);
        break;
      case 'external-crown-plotting':
        file_path = path.join(file_path, 'Customer', `Customer_CROWN.svg`);
        break;
      case 'internal-pavilion-plotting':
        file_path = path.join(file_path, 'Internal', `Internal_PAVILION.svg`);
        break;
      case 'external-pavilion-plotting':
        file_path = path.join(file_path, 'Customer', `Customer_CROWN.svg`);
        break;
      case 'internal-comments':
        file_path = path.join(file_path, 'Internal', `Internal_comments.txt`);
        break;
      case 'external-comments':
        file_path = path.join(file_path, 'Customer', `Customer_comments.txt`);
        break;
      default:
        throw new Error('Please provide valid file type');
    }
    let content_type;
    if (file_path.endsWith('.txt')) {
      content_type = 'txt/plain';
    } else {
      content_type = 'image/svg+xml';
    }
    let file_content = await fs.readFile(file_path);
    res.setHeader('Content-type', content_type);
    res.status(200).send(file_content);
    return;
  } catch (err) {
    console.error(`Error in getting stone plotting files`, err);
    res.error({
      errors: err,
      status_code: 500,
    });
  }
};

export const get_presigned_urls = async(req, res) => {
  try {
    let request_no = req?.params?.request_no;
    if (!request_no) {
      throw new Error('Please provide valid request number.');
    }

    const entries = await Promise.all(
      Object.entries(files_to_presign).map(async ([response_key, s3_key]) => {
        let contentType;
        if (s3_key.endsWith('.svg')) {
          contentType = 'image/svg+xml';
        } else {
          contentType = 'text/plain';
        }
        const url = await generate_presigned_url(process.env.S3_BUCKET_NAME, `${request_no}/${s3_key}`, 3600, contentType);
        return [response_key, url];
      })
    );
    const result = Object.fromEntries(entries);
    res.success({
      data: result,
      status_code: 200,
    });
  } catch (err) {
    console.error('Error in generating pre-signed urls', err);
    res.error({
      errors: err,
      status_code: 500,
    })
  }
}