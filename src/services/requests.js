import {
  transform_measurement_svg,
  transform_fancy_stone_measurement_svg,
} from '../utils/transform_measurement_svg.js';
import fs from 'fs/promises';
import dotenv from 'dotenv';
import path from 'path';

export const get_stone_measurements = async(req, res) => {
  try {
    let request_no = req?.params?.request_no;
    if (!request_no) {
      throw new Error('Please provide valid request number.');
    }
    let file_content = await fs.readFile(path.join(process.env.COMMON_STORAGE_PATH, request_no, 'report.txt'));
    let parsed_content = dotenv.parse(file_content);
    if (!parsed_content) {
      throw new Error(`Error in parsing the measurement text file for ${request_no}`);
    }
    let stone_measurements = {
      length: parsed_content.DIAMETER_MM_MAX ?? parsed_content.LENGTH_MM ?? null,
      width: parsed_content.DIAMETER_MM_MIN ?? parsed_content.WIDTH_MM,
      depth: parsed_content.TOTAL_DEPTH_MM ?? null,
      depth_percentage: parsed_content.TOTAL_DEPTH_PC ?? null,
      table: parsed_content.TABLE_PC ?? parsed_content.WIDTH_TABLE_PC,
      crown_angle: parsed_content.CROWN_ANGLE_DEG ?? parsed_content.CROWN_FANCY_CURVE_ANGLE_DEG,
      crown_percent: parsed_content.CROWN_HEIGHT_GIA_ROUNDED ?? null,
      crown_height: parsed_content.CROWN_HEIGHT_PC ?? null,
      pav_angle: parsed_content.PAVILION_ANGLE_DEG ?? parsed_content.PAVILION_FANCY_SHOULDER_ANGLE_DEG ?? null,
      pav_percent: parsed_content.PAVILION_DEPTH_GIA_ROUNDED ?? null,
      pav_depth: parsed_content.PAVILION_HEIGHT_PC_AVG ?? null,
      girdle_percent: parsed_content.GIRDLE_WIDE_BEZEL ?? null,
      girdle_min_percent: parsed_content.GIRDLE_NARROW_MIN ?? null,
      girdle_max_percent: parsed_content.GIRDLE_NARROW_MAX ?? null,
      culet_size_percent: parsed_content.CULET_PC ?? null,
      table_off_center_percent: parsed_content.OFFSET_GIRDLE_TABLE_PC ?? parsed_content.OFFSETX_GIRDLE_TABLE_PC ?? null,
      culet_off_center_percent: parsed_content.OFFSET_GIRDLE_CULET_PC ?? parsed_content.OFFSETX_GIRDLE_CULET_PC ?? null,
      girdle_min: parsed_content.GIRDLE_THICKNESS_MIN_GIA_ROUNDED ?? null,
      girdle_max: parsed_content.GIRDLE_THICKNESS_MAX_GIA_ROUNDED ?? null,
      culet_size: parsed_content.CULET_SIZE_GIA_ROUNDED ?? null,
      cut_grade: parsed_content.OVERALL_FINAL_GRADE ?? null,
      symmetry_grade: parsed_content.OVERALL_SYM ?? null,
      star_length_percent: parsed_content.STAR_RATIO_PC ?? parsed_content.STAR_RATIO_PC ?? null,
      lower_girdle_length_percent: parsed_content.LENGTH_GIRDLE_FACET ?? parsed_content.LENGTH_GIRDLE_FACET ?? null,
    };
    res.success({
      data: stone_measurements,
      status_code: 200,
    });
  } catch (err) {
    console.error(`Error in getting measurements for request id - ${req?.query?.request_id}`, err);
    res.error({
      errors: err,
      status_code: 500,
    })
  }
};

export const get_stone_measurement_diagram = async(req, res) => {
  try {
    let request_no = req?.params?.request_no;
    if (!request_no) {
      throw new Error('Please provide valid request no.');
    }
    let is_fancy_shape = req?.query?.is_fancy_shape === 'true'
      ? true
      : false;
    let updated_diagram;
    if (is_fancy_shape) {
      updated_diagram = await transform_fancy_stone_measurement_svg(request_no);
    } else {
      updated_diagram = await transform_measurement_svg(request_no);
    }
    res.setHeader('Content-type', 'image/svg');
    res.status(200).send(updated_diagram);
    return;
  } catch (err) {
    console.error(`Error in getting stone measurement diagram`, err);
    res.error({
      errors: err,
      status_code: 500,
    });
  }
};