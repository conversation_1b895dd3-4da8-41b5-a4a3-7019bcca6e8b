import dotenv from 'dotenv';
dotenv.config();
import express from 'express';
import { response } from './src/middlewares/response.js';
import cors from 'cors';
const app = express();
import { get_stone_measurements, get_stone_measurement_diagram } from './src/services/requests.js';

const api_prefix = '/apis';
const port = process.env.PORT || 5101;

app.use(express.json());

app.use(response);

let cors_options = {
  origin: (req_origin, callback) => {
    if (!req_origin) {
      return callback(null, true);
    }
    let allowed_regex = /^(http:\/\/localhost:\d+|https:\/\/[a-zA-Z0-9_-]+\.nivodaapi\.net|https:\/\/[a-zA-Z0-9_-]+\.nivoda\.com)$/
    const is_origin_allowed = allowed_regex.test(req_origin);

    if (is_origin_allowed) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS: ' + req_origin));
    }
  }
}

app.use(cors(cors_options));

app.get(`${api_prefix}/health_check`, (req, res) => {
  res.success({
    status: 200,
    message: `Grading file fetcher server is running at ${port}!`,
  });
});

app.get(`${api_prefix}/v1/requests/:request_no/measurements`, async (req, res) => {
  return await get_stone_measurements(req, res);
});

app.get(`${api_prefix}/v1/requests/:request_no/measurement-diagram`, async (req, res) => {
  return await get_stone_measurement_diagram(req, res);
});

app.listen(port, (error) => {
  if (error) {
    console.error(`❌❌❌ Error in starting server on ${port}`, error);
  } else {
    console.log(`✅✅✅ Server running on ${port}`);
  }
}); 