<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="500" height="500" viewBox="0 0 500 500" xml:space="preserve">
<desc>Created with Fabric.js 5.1.0</desc>
<defs>
</defs>
<g transform="matrix(0.04 0 0 0.04 250 250)"  >
<path style="stroke: rgb(135,135,135); stroke-width: 30; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-opacity: 0; fill-rule: nonzero; opacity: 1;"  transform=" translate(-5502, -5539.5)" d="M 4047 242 L 4327 172 L 4594 119 L 4840 82 M 3750 332 L 4047 242 M 5079 57 L 5327 44 L 5390 43 M 4840 82 L 5079 57 M 3545 404 L 3750 332 M 3387 468 L 3545 404 M 7353 358 L 7504 413 L 7582 446 M 6136 79 L 6348 107 M 5993 64 L 6136 79 M 6470 128 L 6723 178 L 6850 209 M 5538 42 L 5689 45 M 5455 43 L 5538 42 M 5689 45 L 5820 52 M 6348 107 L 6470 128 M 7240 320 L 7353 358 M 7011 252 L 7240 320 M 7326 348 L 7353 358 M 5820 52 L 5993 64 M 6850 209 L 7011 252 M 8211 753 L 8468 908 L 8693 1058 M 7970 623 L 8211 753 M 9165 1436 L 9298 1554 L 9364 1619 M 7743 515 L 7970 623 M 7582 446 L 7743 515 M 8693 1058 L 8817 1149 L 8949 1254 L 9043 1330 L 9165 1436 M 5976 4423 L 7582 446 M 10280 2802 L 10400 3023 M 10174 2625 L 10280 2802 M 9810 2113 L 9966 2317 L 10101 2513 M 9612 1878 L 9810 2113 M 10400 3023 L 10478 3185 M 9460 1713 L 9612 1878 M 9400 1654 L 9460 1713 M 10101 2513 L 10174 2625 M 10478 3185 L 10545 3336 L 10578 3414 M 10728 3817 L 10776 3978 M 10652 3601 L 10728 3817 M 10926 4663 L 10956 4864 L 10985 5141 M 10993 5255 L 11001 5463 L 11001 5555 M 10776 3978 L 10841 4220 M 10597 3459 L 10652 3601 M 10578 3414 L 10597 3459 M 10841 4220 L 10882 4412 L 10895 4477 L 10926 4663 M 10985 5141 L 10993 5255 M 10993 5260 L 10993 5255 M 6613 5066 L 10578 3414 M 10947 6298 L 10906 6543 M 10968 6119 L 10947 6298 M 10868 6720 L 10814 6939 L 10739 7206 M 10644 7475 L 10571 7646 L 10533 7729 M 10704 7310 L 10644 7475 M 10906 6543 L 10868 6720 M 10739 7206 L 10704 7310 M 10984 5960 L 10968 6119 M 10997 5730 L 10984 5960 L 10984 5956 M 10999 5614 L 10997 5730 M 10140 8424 L 9908 8759 L 9768 8950 M 10299 8176 L 10140 8424 M 10379 8040 L 10299 8176 M 10481 7844 L 10379 8040 M 10533 7729 L 10481 7844 M 9532 9251 L 9408 9394 L 9354 9449 M 9631 9129 L 9532 9251 L 9528 9256 M 9768 8950 L 9631 9129 M 6587 5973 L 10533 7729 M 8208 10325 L 7977 10450 L 7772 10548 M 8451 10179 L 8208 10325 M 8639 10053 L 8451 10179 M 9256 9549 L 9087 9705 M 9321 9483 L 9256 9549 M 9087 9705 L 8976 9799 M 8778 9954 L 8639 10053 M 8845 9902 L 8778 9954 M 7772 10548 L 7636 10608 L 7586 10628 M 8976 9799 L 8845 9902 M 7472 10675 L 7211 10767 M 7586 10628 L 7472 10675 M 7211 10767 L 6977 10837 L 6719 10901 L 6456 10954 L 6209 10991 L 5923 11020 L 5674 11034 L 5541 11035 M 5968 6661 L 7586 10628 M 4584 10962 L 4401 10928 L 4218 10886 M 3732 10739 L 3577 10685 L 3438 10629 M 5294 11031 L 5157 11025 L 4879 11002 M 4750 10987 L 4584 10962 M 5495 11035 L 5294 11031 M 5498 11035 L 5495 11035 M 4879 11002 L 4750 10987 M 3889 10792 L 3732 10739 M 4104 10856 L 3889 10792 M 4218 10886 L 4104 10856 M 3075 10467 L 2874 10354 L 2713 10254 M 3216 10534 L 3075 10467 M 2524 10132 L 2352 10013 L 2185 9890 M 2024 9767 L 1813 9595 L 1650 9449 M 3359 10597 L 3216 10534 M 3438 10629 L 3359 10597 M 2713 10254 L 2524 10132 M 2185 9890 L 2024 9767 M 5079 6620 L 3438 10629 M 1149 8892 L 993 8682 L 826 8433 M 1313 9094 L 1149 8892 M 1468 9267 L 1313 9094 M 1609 9409 L 1468 9267 M 826 8433 L 716 8249 L 614 8061 M 520 7870 L 467 7749 L 431 7660 M 542 7916 L 520 7870 M 614 8061 L 542 7916 M 48 6197 L 24 5964 L 12 5735 M 128 6680 L 89 6467 M 176 6888 L 128 6680 M 377 7528 L 312 7343 M 431 7660 L 377 7528 M 89 6467 L 48 6197 M 211 7017 L 176 6888 M 261 7185 L 211 7017 M 12 5735 L 10 5620 L 10 5561 M 312 7343 L 261 7185 M 4332 6032 L 431 7660 M 235 3970 L 305 3750 M 178 4185 L 235 3970 M 11 5386 L 26 5095 M 11 5536 L 11 5386 M 26 5095 L 49 4868 M 305 3750 L 407 3476 L 434 3414 M 49 4868 L 85 4625 L 138 4359 L 178 4185 M 880 2567 L 1004 2381 L 1136 2202 M 629 2995 L 763 2755 M 541 3172 L 629 2995 M 763 2755 L 880 2567 M 1373 1913 L 1521 1753 L 1582 1691 M 1235 2077 L 1373 1913 M 472 3323 L 541 3172 M 434 3414 L 472 3323 M 1136 2202 L 1235 2077 M 4415 5089 L 434 3414 M 1839 1445 L 2043 1274 L 2243 1120 M 2385 1019 L 2579 889 L 2785 765 M 3090 601 L 3322 495 L 3387 468 M 2923 688 L 3090 601 M 1662 1610 L 1839 1445 M 1634 1638 L 1662 1610 M 2243 1120 L 2385 1019 M 2785 765 L 2923 688 M 3387 468 L 4973 4341 M 5390 43 L 5455 43 M 5013 4405 L 5390 43 M 5455 43 L 5953 4459 M 5976 4423 L 9364 1619 L 9400 1654 M 5953 4459 L 5976 4423 M 9400 1654 L 6613 5066 L 6579 5087 M 5520 5519 L 5953 4459 M 5532 5531 L 5520 5519 L 5488 5520 M 11001 5555 L 11001 5558 L 10999 5614 M 6579 5087 L 11001 5555 M 10999 5614 L 6587 5973 L 6579 5971 M 5532 5531 L 6579 5087 M 5532 5539 L 5532 5531 M 9354 9449 L 9321 9483 M 6579 5971 L 9354 9449 M 9321 9483 L 5952 6590 M 5532 5539 L 6579 5971 M 5512 5560 L 5532 5539 M 5968 6661 L 5541 11035 L 5498 11035 M 5952 6590 L 5968 6661 M 5498 11035 L 5103 6578 M 5512 5560 L 5952 6590 M 5490 5560 L 5512 5560 M 1650 9449 L 1637 9438 L 1609 9409 M 5079 6620 L 1650 9449 M 5103 6578 L 5079 6620 M 1609 9409 L 4368 6023 M 5490 5560 L 5103 6578 M 5475 5546 L 5490 5560 M 4332 6032 L 10 5561 L 11 5536 M 4368 6023 L 4332 6032 M 11 5536 L 4431 5099 M 5475 5546 L 4368 6023 M 5475 5532 L 5475 5546 M 4415 5089 L 1582 1691 L 1634 1638 M 4431 5099 L 4415 5089 M 1634 1638 L 4973 4341 L 5013 4405 L 5488 5520 L 5475 5532 L 4431 5099 M 4325 170 L 4046 239 M 4586 118 L 4325 170 M 4046 239 L 3749 328 M 5325 41 L 5075 56 M 5450 40 L 5325 41 M 5075 56 L 4829 81 L 4586 118 M 3749 328 L 3541 401 L 3389 462 M 5451 40 L 5450 40 M 6346 106 L 6134 77 L 5993 63 M 6721 176 L 6469 126 M 6846 207 L 6721 176 M 5688 43 L 5533 40 L 5451 40 M 5820 51 L 5688 43 M 6469 126 L 6346 106 M 7326 348 L 7224 314 L 7004 249 M 5993 63 L 5820 51 M 7004 249 L 6846 207 M 11001 5556 L 11002 5465 L 10993 5260 M 11001 5571 L 11001 5556 M 10984 5956 L 10997 5725 L 11001 5571 M 9405 9398 L 9528 9256 M 9356 9447 L 9405 9398 M 7968 10455 L 8205 10327 M 7768 10552 L 7968 10455 M 8205 10327 L 8450 10180 L 8637 10054 M 9088 9705 L 9256 9549 L 9356 9447 M 8975 9799 L 9088 9705 M 8637 10054 L 8776 9955 L 8843 9904 M 7637 10610 L 7768 10552 M 7584 10631 L 7637 10610 M 8843 9904 L 8975 9799 M 7208 10770 L 7471 10677 L 7584 10631 M 6968 10841 L 7208 10770 M 6712 10905 L 6968 10841 M 6456 10956 L 6712 10905 M 6208 10994 L 6456 10956 M 5922 11024 L 6208 10994 M 5674 11038 L 5922 11024 M 5554 11039 L 5674 11038 M 4402 10929 L 4591 10964 M 4217 10887 L 4402 10929 M 3566 10683 L 3711 10734 M 3452 10637 L 3566 10683 M 5161 11029 L 5298 11034 M 4894 11006 L 5161 11029 M 4591 10964 L 4777 10992 M 5298 11034 L 5495 11039 L 5554 11039 M 4777 10992 L 4894 11006 M 3711 10734 L 3887 10792 L 4102 10856 L 4217 10887 M 2875 10356 L 3077 10469 M 2713 10255 L 2875 10356 M 3077 10469 L 3215 10535 M 2349 10013 L 2522 10132 M 2179 9887 L 2349 10013 M 1635 9440 L 1810 9596 M 1603 9407 L 1635 9440 M 1810 9596 L 2018 9765 M 3215 10535 L 3357 10598 L 3452 10637 M 2522 10132 L 2713 10255 M 2018 9765 L 2179 9887 M 986 8676 L 1142 8887 M 821 8429 L 986 8676 M 1142 8887 L 1311 9095 L 1467 9269 L 1603 9407 M 712 8248 L 821 8429 M 607 8054 L 712 8248 M 459 7746 L 512 7863 M 415 7637 L 459 7746 M 512 7863 L 530 7902 L 607 8054 M 18 5959 L 42 6186 M 5 5723 L 18 5959 M 84 6463 L 124 6681 L 172 6889 M 306 7342 L 371 7527 L 415 7637 M 42 6186 L 84 6463 M 172 6889 L 206 7017 L 256 7185 M 2 5609 L 5 5723 M 2 5533 L 2 5609 M 256 7185 L 306 7342 M 298 3750 L 229 3967 L 177 4165 M 20 5096 L 4 5383 L 2 5533 M 43 4870 L 20 5096 M 383 3516 L 298 3750 M 433 3397 L 383 3516 M 81 4621 L 43 4870 M 136 4342 L 81 4621 M 177 4165 L 136 4342 M 1002 2379 L 876 2567 M 1134 2199 L 1002 2379 M 758 2757 L 617 3009 L 532 3178 M 876 2567 L 758 2757 M 1518 1748 L 1370 1910 M 1589 1676 L 1518 1748 M 1370 1910 L 1233 2073 M 532 3178 L 458 3336 L 433 3397 M 1233 2073 L 1134 2199 M 2044 1267 L 1841 1437 M 2243 1115 L 2044 1267 M 2577 886 L 2384 1015 M 2780 763 L 2577 886 M 3310 494 L 3076 603 M 3389 462 L 3310 494 M 3076 603 L 2913 688 M 1841 1437 L 1661 1604 L 1589 1676 M 2384 1015 L 2243 1115 M 2913 688 L 2780 763" stroke-linecap="round" />
</g>
<g transform="matrix(0.99 -0.16 0.16 0.99 453.89 248.5)"  >
<path style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;"  transform=" translate(-448.89, -246.5)" d="M 450.890625 233 L 450.890625 237 L 446.890625 247 L 446.890625 255 L 446.890625 260" stroke-linecap="round" />
</g>
<g transform="matrix(1 0 0 1 116.39 315)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 -18 -32)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-5" y1="-27.5" x2="5" y2="27.5" />
</g>
		<g transform="matrix(1 0 0 1 5 27.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-18" y1="-32" x2="18" y2="32" />
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 109.89 321)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 0 0)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0" y1="0" x2="0" y2="0" />
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 0 0)"  >
<path style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;"  transform=" translate(0, 0)" d="M L L 121.890625 257 L L" stroke-linecap="round" />
</g>
</svg>