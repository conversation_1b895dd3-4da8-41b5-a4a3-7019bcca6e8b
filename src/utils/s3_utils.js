import 'dotenv/config'
import { S3Client, GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const s3_client = new S3Client({
  region: 'eu-west-2', // change this
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

/**
 * Generate a pre-signed GET URL for a single S3 object
 * @param {string} bucket_name - S3 bucket name
 * @param {string} object_key - S3 object key
 * @param {number} expires_in - Expiration time in seconds (default: 3600)
 * @returns {Promise<string>} - Pre-signed URL
 */
export const generate_presigned_url = async (bucket_name, object_key, expires_in = 3600) => {
  const command = new PutObjectCommand({
    Bucket: bucket_name,
    Key: object_key,
  });

  const signed_url = await getSignedUrl(s3_client, command, { expiresIn: expires_in });
  return signed_url;
}

