{"name": "grading-files-fetcher-server", "version": "1.0.0", "description": "This server is to be deployed on grader's machine to fetch various files related to grading, generated by various equipments.", "main": "index.js", "scripts": {"start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "engines": {"node": "22.16.0"}, "author": "", "license": "ISC", "dependencies": {"cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "postcss": "^8.5.4", "postcss-safe-parser": "^7.0.1"}, "type": "module"}