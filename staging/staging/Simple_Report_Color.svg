<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" viewBox="-2256 947 15907 9560" xmlns="http://www.w3.org/2000/svg">
	<style type="text/css">
		<![CDATA[
			.cw-plot-diamond-filler {
				fill: #5a5a5a;
				stroke: #c0c0c0;
				stroke-width: 30;
			}
			.cw-plot-diamond-outline {
				fill: transparent;
				stroke: #000000;
				stroke-width: 30;
			}
			.cw-plot-dimension {
				stroke: #000000;
				stroke-width: 20;
			}
			.cw-plot-path-1 {
				fill: #000000;
				stroke: #000000;
				stroke-width: 20;
			}
			.cw-plot-text-backdrop {
				fill: #ffffff;
			}
			.cw-plot-text-label {
				fill: #4a90e2;
				font-size: 352px;
				font-weight: normal;
			}
			line {
				stroke-linecap: round;
			}
			path {
				stroke-linejoin: round;
			}
			polygon {
				stroke-linejoin: round;
			}
			text {
				font-family: Arial;
			}
		]]>
	</style>
	<g>
		<path class="cw-plot-path-1" d="M 8246 3350 L 9648 3350 L 9648 3349 L 8703 2767 L 7533 2767 L 8246 3350 M 1414 3350 L 3601 3350 L 3375 2767 L 2356 2767 L 1415 3349 L 1414 3350 M 3760 3900 L 6438 3900 L 6094 3350 L 3602 3350 L 3760 3900 M 6438 3900 L 8780 3900 L 8246 3350 L 6094 3350 L 6094 3350 L 6438 3900 M 3904 4640 L 6748 4640 L 6438 3900 L 3760 3900 L 3904 4640 M 9261 4640 L 8781 3901 L 8780 3900 L 6438 3900 L 6748 4640 L 9261 4640 M 6477 6800 L 8437 6799 L 9261 5079 L 6749 5079 L 6477 6800 M 4258 6800 L 4258 6800 L 6477 6800 L 6477 6800 L 6749 5079 L 6748 5079 L 3903 5079 L 3903 5079 L 4258 6800 M 6169 8167 L 7502 8167 L 8437 6800 L 8437 6799 L 6477 6800 L 6477 6800 L 6169 8167 M 5848 9164 L 5849 9164 L 6528 9164 L 6528 9164 L 7502 8167 L 6169 8167 L 6169 8167 L 5848 9164 M 5079 9164 L 5848 9164 L 6169 8167 L 4660 8167 L 4660 8167 L 5079 9164 M 4042 9164 L 5079 9164 L 5079 9164 L 4660 8167 L 2627 8167 L 4042 9164 L 4042 9164 M 5079 9164 L 5514 9886 L 5515 9887 L 5515 9886 L 5849 9164 L 5848 9164 L 5079 9164 L 5079 9164" />
		<path class="cw-plot-diamond-filler" d="M 9261 5079 L 11000 5078 L 11000 4641 L 9261 4640 L 9261 5079 M 3903 5079 L 6748 5079 L 6748 4640 L 3904 4640 L 3903 4640 L 3903 5079 M 6748 5079 L 6749 5079 L 9261 5079 L 9261 5079 L 9261 4640 L 6748 4640 L 6748 5079 M 69 5079 L 69 5079 L 69 4640 L 69 4640 L 0 4640 L 0 4641 L 0 5078 L 0 5079 L 69 5079 L 69 5079 L 3903 5079 L 3903 5079 L 3903 4640 L 69 4640 L 69 5079" />
		<path class="cw-plot-diamond-outline" d="M 2297 2767 L 3467 2767 L 5372 2767 L 7625 2767 M 2297 2767 L 2356 2767 M 2297 2767 L 1352 3349 M 8703 2767 L 7533 2767 M 8703 2767 L 8644 2767 M 8703 2767 L 9648 3349 M 7533 2767 L 5628 2767 M 7533 2767 L 8246 3350 M 5628 2767 L 3375 2767 M 5628 2767 L 6094 3350 M 8644 2767 L 7625 2767 M 2356 2767 L 3375 2767 M 2356 2767 L 1415 3349 M 3375 2767 L 3601 3350 M 9648 3349 L 9648 3350 M 1352 3349 L 1415 3349 M 1352 3349 L 1352 3350 M 1415 3349 L 1414 3350 M 1352 3350 L 639 3901 M 8246 3350 L 9648 3350 M 8246 3350 L 8246 3350 M 9648 3350 L 10361 3901 M 8246 3350 L 6094 3350 M 8246 3350 L 8780 3900 M 6094 3350 L 6094 3350 L 3602 3350 M 6094 3350 L 6438 3900 M 1414 3350 L 3601 3350 M 1414 3350 L 706 3900 M 3601 3350 L 3602 3350 L 3760 3900 M 639 3901 L 639 3901 L 705 3901 M 639 3901 L 0 4640 M 10361 3901 L 10361 3901 L 10361 3901 L 8781 3901 M 10361 3901 L 11000 4640 M 8781 3901 L 8780 3900 M 8781 3901 L 9261 4640 M 8780 3900 L 6438 3900 L 3760 3900 M 6438 3900 M 3760 3900 M 6438 3900 L 6748 4640 M 705 3901 L 706 3900 M 705 3901 L 69 4640 M 706 3900 L 3760 3900 L 3904 4640 M 6748 4640 L 3904 4640 M 6748 4640 M 3904 4640 L 3903 4640 L 69 4640 M 6748 4640 L 9261 4640 L 11000 4641 L 11000 4640 M 69 4640 L 69 4640 L 69 4640 L 0 4640 M 0 4641 L 0 4640 M 0 5078 L 0 5079 M 9261 5079 L 11000 5078 M 9261 5079 L 9261 5079 M 11000 5078 L 11000 5079 L 9793 6800 M 0 5079 L 69 5079 M 0 5079 L 1207 6800 M 9261 5079 L 6749 5079 M 9261 5079 L 8437 6799 M 6748 5079 L 3903 5079 M 6748 5079 L 6749 5079 M 3903 5079 L 3903 5079 M 6749 5079 L 6477 6800 M 69 5079 L 69 5079 L 69 5079 L 1267 6799 M 69 5079 L 3903 5079 L 4258 6800 M 9793 6800 L 9793 6800 L 9793 6800 L 8437 6800 M 9793 6800 L 8424 8167 M 1207 6800 L 1207 6800 L 1207 6800 L 2576 8167 M 1207 6800 L 1267 6799 L 1267 6800 M 8437 6799 L 6477 6800 M 8437 6799 L 8437 6800 M 6477 6800 L 6477 6800 M 8437 6800 L 7502 8167 M 6477 6800 L 4258 6800 M 6477 6800 L 6169 8167 M 4258 6800 L 4258 6800 L 4258 6800 L 4660 8167 M 4258 6800 L 1267 6800 L 2627 8167 M 2576 8167 L 2576 8167 M 8424 8167 L 8424 8167 L 8424 8167 L 7502 8167 M 8424 8167 L 6999 9164 M 2627 8167 L 2576 8167 M 2627 8167 M 2576 8167 L 4001 9164 M 7502 8167 L 6528 9164 M 7502 8167 L 6169 8167 L 6169 8167 L 4660 8167 M 6169 8167 L 5848 9164 M 4660 8167 L 4660 8167 L 2627 8167 M 4660 8167 L 5079 9164 M 2627 8167 L 4042 9164 M 6528 9164 L 6999 9164 M 6528 9164 L 6528 9164 M 6999 9164 L 6999 9164 L 6999 9164 L 5515 9887 M 4001 9164 L 4001 9164 L 4001 9164 L 5485 9887 M 4001 9164 L 4042 9164 L 4042 9164 M 5485 9887 L 5515 9887 M 6528 9164 L 5849 9164 M 6528 9164 L 5515 9886 M 5849 9164 L 5848 9164 M 5849 9164 L 5515 9886 M 5848 9164 L 5079 9164 L 5079 9164 M 5515 9886 L 5515 9887 L 5514 9886 M 5079 9164 L 4042 9164 M 5079 9164 L 5514 9886 M 4042 9164 L 5514 9886" />
		<line class="cw-plot-dimension" x1="-801" x2="-401" y1="2767" y2="2767" />
		<line class="cw-plot-dimension" x1="-601" x2="-601" y1="2767" y2="4640" />
		<line class="cw-plot-dimension" x1="-801" x2="-401" y1="4640" y2="4640" />
		<line class="cw-plot-dimension" x1="-1807" x2="-801" y1="4240" y2="4640" />
		<line class="cw-plot-dimension" x1="-1807" x2="-801" y1="5479" y2="5079" />
		<line class="cw-plot-dimension" x1="-801" x2="-401" y1="5079" y2="5079" />
		<line class="cw-plot-dimension" x1="-601" x2="-601" y1="5079" y2="9887" />
		<line class="cw-plot-dimension" x1="-801" x2="-401" y1="9887" y2="9887" />
		<line class="cw-plot-dimension" x1="11200" x2="11973" y1="4640" y2="4640" />
		<line class="cw-plot-dimension" x1="11973" x2="11687" y1="4640" y2="4309" />
		<line class="cw-plot-dimension" x1="11200" x2="11973" y1="5079" y2="5079" />
		<line class="cw-plot-dimension" x1="11973" x2="11722" y1="5079" y2="5437" />
		<line class="cw-plot-dimension" x1="11973" x2="12373" y1="2767" y2="2767" />
		<line class="cw-plot-dimension" x1="12173" x2="12173" y1="2767" y2="9887" />
		<line class="cw-plot-dimension" x1="11973" x2="12373" y1="9887" y2="9887" />
		<line class="cw-plot-dimension" x1="5500" x2="5500" y1="10487" y2="10087" />
		<line class="cw-plot-dimension" x1="5500" x2="5500" y1="10287" y2="10287" />
		<line class="cw-plot-dimension" x1="5500" x2="5500" y1="10487" y2="10087" />
		<line class="cw-plot-dimension" x1="1724" x2="1724" y1="2567" y2="2167" />
		<line class="cw-plot-dimension" x1="1724" x2="9276" y1="2367" y2="2367" />
		<line class="cw-plot-dimension" x1="9276" x2="9276" y1="2567" y2="2167" />
		<line class="cw-plot-dimension" x1="0" x2="0" y1="1967" y2="1567" />
		<line class="cw-plot-dimension" x1="0" x2="11000" y1="1767" y2="1767" />
		<line class="cw-plot-dimension" x1="11000" x2="11000" y1="1967" y2="1567" />
		<g>
			<rect class="cw-plot-text-backdrop" height="312" width="1203" x="-1202" y="3524" />
			<text class="cw-plot-text-label" x="-1202" y="3803">17.10%</text>
		</g>
		<g>
			<rect class="cw-plot-text-backdrop" height="312" width="1006" x="-2007" y="4680" />
			<text class="cw-plot-text-label" x="-2007" y="4959">4.00%</text>
		</g>
		<g>
			<rect class="cw-plot-text-backdrop" height="312" width="1203" x="-1202" y="7303" />
			<text class="cw-plot-text-label" x="-1202" y="7582">43.89%</text>
		</g>
		<g>
			<rect class="cw-plot-text-backdrop" height="312" width="1031" x="11172" y="3929" />
			<text class="cw-plot-text-label" x="11172" y="4208">49.19&#xb0;</text>
		</g>
		<g>
			<rect class="cw-plot-text-backdrop" height="312" width="1031" x="11207" y="5457" />
			<text class="cw-plot-text-label" x="11207" y="5737">55.00&#xb0;</text>
		</g>
		<g class="cw-plot-text-total-depth">
			<g>
				<rect class="cw-plot-text-backdrop" height="312" width="1203" x="11572" y="7303" />
				<text class="cw-plot-text-label" x="11572" y="7582">64.99%</text>
			</g>
			<g>
				<rect class="cw-plot-text-backdrop" height="312" width="1581" x="11383" y="7703" />
				<text class="cw-plot-text-label" x="11387" y="7982">4.293 mm</text>
			</g>
		</g>
		<g>
			<rect class="cw-plot-text-backdrop" height="312" width="1006" x="5700" y="10107" />
			<text class="cw-plot-text-label" x="5700" y="10386">0.00%</text>
		</g>
		<g class="cw-plot-text-table-pc">
			<rect class="cw-plot-text-backdrop" height="312" width="3616" x="3692" y="2187" />
			<text class="cw-plot-text-label" x="3706" y="2466">68.65% ( min 58.10% )</text>
		</g>
		<g>
			<rect class="cw-plot-text-backdrop" height="312" width="1006" x="4997" y="1587" />
			<text class="cw-plot-text-label" x="5001" y="1866">100 %</text>
		</g>
		<g class="cw-plot-text-main-measuring-mm">
			<rect class="cw-plot-text-backdrop" height="312" width="6428" x="2286" y="987" />
			<text class="cw-plot-text-label" x="2311" y="1266">W: 6.606 mm, L: 13.950 mm, L/W: 2.112</text>
		</g>
		<g class="cw-plot-text-star-ratio">
			<g>
				<rect class="cw-plot-text-backdrop" height="312" width="653" x="12573" y="2787" />
				<text class="cw-plot-text-label" x="12573" y="3066">Star</text>
			</g>
			<g>
				<rect class="cw-plot-text-backdrop" height="312" width="828" x="12573" y="3187" />
				<text class="cw-plot-text-label" x="12573" y="3466">Ratio</text>
			</g>
			<g>
				<rect class="cw-plot-text-backdrop" height="312" width="594" x="12573" y="3587" />
				<text class="cw-plot-text-label" x="12573" y="3866">N/A</text>
			</g>
		</g>
		<g class="cw-plot-text-length-girdle-facet">
			<g>
				<rect class="cw-plot-text-backdrop" height="312" width="1084" x="0" y="8307" />
				<text class="cw-plot-text-label" x="0" y="8586">Length</text>
			</g>
			<g>
				<rect class="cw-plot-text-backdrop" height="312" width="947" x="0" y="8707" />
				<text class="cw-plot-text-label" x="0" y="8986">Girdle</text>
			</g>
			<g>
				<rect class="cw-plot-text-backdrop" height="312" width="891" x="0" y="9107" />
				<text class="cw-plot-text-label" x="0" y="9386">Facet</text>
			</g>
			<g>
				<rect class="cw-plot-text-backdrop" height="312" width="594" x="0" y="9507" />
				<text class="cw-plot-text-label" x="0" y="9786">N/A</text>
			</g>
		</g>
	</g>
</svg>