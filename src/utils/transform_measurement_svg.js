import * as cheerio from 'cheerio';
import fs from 'fs/promises';
import postcss from 'postcss';
import safe_parser from 'postcss-safe-parser';
import path from 'path';

let style_overrides = {
  'cw-plot-diamond-filler': { fill: '#000000', stroke: '#000000' },
  'cw-plot-diamond-outline': { stroke: '#000000' },
  'cw-plot-dimension': { stroke: '#000000' },
  'cw-plot-text-label': { fill: '#000000' },
};

let fancy_style_overrides = {
  'cw-plot-diamond-filler': { fill: '#000000', stroke: '#000000' },
  'cw-plot-diamond-outline': { stroke: '#000000' },
  'cw-plot-path-1': { fill: '#000000', stroke: '#000000' },
  'cw-plot-text-main-facet-short-name': { fill: '#000000' },
};

export const transform_fancy_stone_measurement_svg = async(request_no) => {
  try {
    let diagram_file_content = await fs.readFile(path.join(process.env.COMMON_STORAGE_PATH, request_no, 'PROPORTION_DRAWING_FIELD_LENGTH.svg'), 'utf-8');
    let $ = cheerio.load(diagram_file_content, { xml: true });
    let style_element = $('style');
    if (style_element.length) {
      let css_text = style_element.text();
      let parsed_css = postcss.parse(css_text, { parser: safe_parser });
      parsed_css.walkRules((rule) => {
        let selector = rule.selector?.replace(/^\./, '');
        if (fancy_style_overrides[selector]) {
          const override = fancy_style_overrides[selector];

          rule.walkDecls(decl => {
            if (decl.prop === 'fill' && override.fill) {
              decl.value = override.fill;
            }
            if (decl.prop === 'stroke' && override.stroke) {
              decl.value = override.stroke;
            }
          });
        }
      });
      style_element.text(parsed_css.toString());
    }
    let main_group = $('svg > g').first();

    let path_element = $('path.cw-plot-diamond-outline');
    const g_elements = main_group.children('g');
    let d_attribute = $(path_element).attr('d');
    if (d_attribute) {
      const command_regex = /([a-zA-Z])([^a-zA-Z]*)/g;
      const commands = [];
      let match;

      while ((match = command_regex.exec(d_attribute)) !== null) {
        commands.push(match[1] + match[2]);
      }

      let culet_percent_group = g_elements.eq(7);
      let text_element = culet_percent_group.find('text.cw-plot-text-main-facet-short-name');
      let current_value = text_element.text();
      let start, skip, end, total_commands = commands.length;
      // In case, the culet percent is 0.00% the commands to be removed for extra lines are
      // a) 6 from the bottom of the commands list for the line on the top of the diagram for 100%.
      // b) Keep the six above these commands
      // c) Then delete the 5 commands kept in b).

      // IN case the culet percent is greater than 0.00%, the commands to be removed for extra lines are
      // a) 6 from the bottom of the commands list for the line on the top of the diagram for 100%.
      // b) Keep the six above these commands
      // c) Then delete the 6 commands kept in b).
      if (current_value && Number(current_value.replace('%', '')) === 0.00) {
        start = commands.slice(0, total_commands - 17);
        skip = commands.slice(total_commands - 12, total_commands - 6);
        end = commands.slice(0, total_commands - 6);
      } else {
        start = commands.slice(0, total_commands - 18);
        skip = commands.slice(total_commands - 12, total_commands - 6);
        end = commands.slice(0, total_commands - 6);
      }
      let trimmed = [
        ...start,
        ...skip,
      ]

      // Update the d attribute
      $(path_element).attr('d', trimmed);
    }

    main_group.find('path.cw-plot-path-1').remove();
    // Indices to remove (example: remove 0th, 2nd, and 5th <g>)
    const indices_to_remove = [11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 6, 9, 7];

    // Sort indices in reverse order and remove the elements
    indices_to_remove.sort((a, b) => b - a).forEach(index => { 
      const el = g_elements.eq(index);
      if (el.length) {
        el.remove();
      }
    });

    let table_measurement_group = g_elements.eq(8);
    let text_element = table_measurement_group.find('text.cw-plot-text-main-facet-short-name');
    let current_value = text_element.text();
    text_element.text(`${current_value.split('%')[0]}%`);
    table_measurement_group.find('rect.cw-plot-text-backdrop').attr({
      width: '1400',
    });
    // fs.writeFile('./update_svg.svg', $.xml());
    return $.xml();
  } catch (err) {
    console.error(`Error in transforming measurement diagram for request - ${request_no}`);
    throw err;
  }
};

export const transform_measurement_svg = async(request_no) => {
  try {
    let diagram_file_content = await fs.readFile(path.join(process.env.COMMON_STORAGE_PATH, request_no, 'Simple_Report_Color.svg'), 'utf-8');
    let $ = cheerio.load(diagram_file_content, { xml: true });
    let style_element = $('style');
    if (style_element.length) {
      let css_text = style_element.text();
      let parsed_css = postcss.parse(css_text, { parser: safe_parser });
      parsed_css.walkRules((rule) => {
        let selector = rule.selector?.replace(/^\./, '');
        if (style_overrides[selector]) {
          const override = style_overrides[selector];

          rule.walkDecls(decl => {
            if (decl.prop === 'fill' && override.fill) {
              decl.value = override.fill;
            }
            if (decl.prop === 'stroke' && override.stroke) {
              decl.value = override.stroke;
            }
          });
        }
      });
      style_element.text(parsed_css.toString());
    }
    
    let main_group = $('svg > g').first();

    main_group.find('path.cw-plot-path-1').remove();

    main_group.children('line').slice(-3).remove();
    
    $('g.cw-plot-text-total-depth').children('g').last().remove();
    $('g.cw-plot-text-star-ratio').remove();
    $('g.cw-plot-text-length-girdle-facet').remove();

    $('g.cw-plot-text-table-pc').next('g').filter((_, el) => !$(el).attr('class')).remove();

    let plot_table_pc_group = $('g.cw-plot-text-table-pc');
    let previous_g_group = plot_table_pc_group.prev('g');
    let attributes = previous_g_group.find('text')[0].attribs;

    let text_element = plot_table_pc_group.find('text.cw-plot-text-label');
    let current_value = text_element.text();
    text_element.text(`${current_value.split('%')[0]}%`);
    plot_table_pc_group.find('rect.cw-plot-text-backdrop').attr({
      x: parseInt(attributes.x) - 100,
      width: '1400',
    });
    text_element.attr({
      x: parseInt(attributes.x) - 100,
    });
    previous_g_group.remove();
    $(`line[x1="${parseInt(attributes.x) - 200}"]`).remove(); // TODO - Confirm entire transformation works for stones
    // fs.writeFile('./update_svg.svg', $.xml());
    return $.xml();
  } catch (err) {
    console.error(`Error in transforming measurement diagram for request - ${request_no}`);
    throw err;
  }
};