<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="500" height="500" viewBox="0 0 500 500" xml:space="preserve">
<desc>Created with Fabric.js 5.1.0</desc>
<defs>
</defs>
<g transform="matrix(0.04 0 0 0.04 250 250)"  >
<path style="stroke: rgb(135,135,135); stroke-width: 30; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-opacity: 0; fill-rule: nonzero; opacity: 1;"  transform=" translate(-5498, -5539.5)" d="M 3647 358 L 3496 413 L 3418 446 L 3257 515 M 3674 348 L 3647 358 M 2789 753 L 2532 908 L 2307 1058 M 3030 623 L 2789 753 M 1835 1436 L 1702 1554 L 1636 1619 M 3257 515 L 3030 623 M 2307 1058 L 2183 1149 L 2051 1254 L 1957 1330 L 1835 1436 M 720 2802 L 600 3023 M 826 2625 L 720 2802 M 1190 2113 L 1034 2317 L 899 2513 M 1388 1878 L 1190 2113 M 600 3023 L 522 3185 M 1540 1713 L 1388 1878 M 1600 1654 L 1540 1713 M 899 2513 L 826 2625 M 522 3185 L 455 3336 L 422 3414 M 272 3817 L 224 3978 M 348 3601 L 272 3817 M 74 4663 L 44 4864 L 15 5141 M 224 3978 L 159 4220 M 403 3459 L 348 3601 M 422 3414 L 403 3459 M 159 4220 L 118 4412 L 105 4477 L 74 4663 M 15 5141 L 7 5255 M 7 5260 L 7 5255 M 53 6298 L 94 6543 M 32 6119 L 53 6298 M 132 6720 L 186 6939 L 261 7206 M 356 7475 L 429 7646 L 467 7729 M 296 7310 L 356 7475 M 94 6543 L 132 6720 M 261 7206 L 296 7310 M 16 5960 L 32 6119 M 16 5960 L 16 5956 M 860 8424 L 1092 8759 L 1232 8950 M 701 8176 L 860 8424 M 621 8040 L 701 8176 M 519 7844 L 621 8040 M 467 7729 L 519 7844 M 1369 9129 L 1468 9251 L 1472 9256 M 1232 8950 L 1369 9129 M 1636 1619 L 1600 1654 M 6675 170 L 6954 239 M 6414 118 L 6675 170 M 6954 239 L 7251 328 M 5675 41 L 5925 56 M 5550 40 L 5675 41 M 5925 56 L 6171 81 L 6414 118 M 7251 328 L 7459 401 L 7611 462 M 7105 1747 L 5550 40 M 5549 40 L 5550 40 M 7127 1729 L 7105 1747 M 7104 1750 L 7105 1747 M 3502 412 L 3674 348 M 3378 464 L 3502 412 M 4654 106 L 4866 77 L 5007 63 M 4279 176 L 4531 126 M 4154 207 L 4279 176 M 5312 43 L 5467 40 L 5549 40 M 5180 51 L 5312 43 M 4531 126 L 4654 106 M 3674 348 L 3776 314 L 3996 249 M 5007 63 L 5180 51 M 3996 249 L 4154 207 M 5549 40 L 3892 1713 L 3877 1699 M 3892 1713 L 3894 1718 M 2536 906 L 2791 753 M 2309 1058 L 2536 906 M 2791 753 L 3034 622 M 1695 1562 L 1826 1445 M 1652 1604 L 1695 1562 M 3034 622 L 3259 514 L 3378 464 M 2182 1152 L 2309 1058 M 2051 1255 L 2182 1152 M 1953 1334 L 2051 1255 M 1826 1445 L 1953 1334 M 3378 464 L 3877 1699 L 1672 1626 M 600 3025 L 722 2799 L 830 2621 M 1037 2315 L 1192 2111 M 902 2509 L 1037 2315 M 1192 2111 L 1389 1878 M 521 3190 L 600 3025 M 1389 1878 L 1540 1714 L 1652 1604 M 830 2621 L 902 2509 M 452 3346 L 521 3190 M 405 3459 L 452 3346 M 1652 1604 L 1672 1626 L 1654 3916 M 223 3983 L 271 3822 L 347 3607 M 43 4871 L 75 4662 M 15 5147 L 43 4871 M -1 5556 L -2 5465 L 7 5260 M 160 4221 L 223 3983 M 347 3607 L 402 3465 L 405 3459 M 119 4411 L 160 4221 M 106 4475 L 119 4411 M 75 4662 L 106 4475 M -1 5571 L -1 5556 M 7 5260 L 15 5147 M 405 3459 L 1639 3918 L 14 5572 M 1654 3917 L 1639 3918 M 94 6543 L 53 6299 L 32 6119 M 185 6933 L 132 6719 M 259 7198 L 185 6933 M 431 7649 L 358 7476 M 444 7676 L 431 7649 M 358 7476 L 296 7308 M 132 6719 L 94 6543 M 296 7308 L 259 7198 M 32 6119 L 16 5956 L 3 5725 L -1 5571 L 14 5572 L 1613 7141 M 1092 8759 L 860 8424 M 1233 8950 L 1092 8759 M 860 8424 L 702 8179 L 624 8043 L 523 7848 L 444 7676 M 1595 9398 L 1472 9256 M 1644 9447 L 1595 9398 M 1472 9256 L 1371 9132 L 1233 8950 M 444 7676 L 1613 7141 L 1634 7145 M 3032 10455 L 2795 10327 M 3232 10552 L 3032 10455 M 2795 10327 L 2550 10180 L 2363 10054 M 1912 9705 L 1744 9549 L 1644 9447 M 2025 9799 L 1912 9705 M 2363 10054 L 2224 9955 L 2157 9904 M 3363 10610 L 3232 10552 M 3416 10631 L 3363 10610 M 2157 9904 L 2025 9799 M 1653 9436 L 3943 9393 L 3944 9401 M 3943 9393 L 3947 9382 M 1644 9447 L 1653 9436 M 1641 7152 L 1653 9436 M 3792 10770 L 3529 10677 L 3416 10631 M 4032 10841 L 3792 10770 M 4288 10905 L 4032 10841 M 4544 10956 L 4288 10905 M 4792 10994 L 4544 10956 M 5078 11024 L 4792 10994 M 5326 11038 L 5078 11024 M 5446 11039 L 5326 11038 M 3416 10631 L 3944 9401 L 5446 11037 M 6598 10929 L 6409 10964 M 6783 10887 L 6598 10929 M 7434 10683 L 7289 10734 M 7548 10637 L 7434 10683 M 5839 11029 L 5702 11034 M 6106 11006 L 5839 11029 M 6409 10964 L 6223 10992 M 5702 11034 L 5505 11039 L 5446 11039 M 6223 10992 L 6106 11006 M 7289 10734 L 7113 10792 L 6898 10856 L 6783 10887 M 5446 11037 L 7122 9451 L 7126 9453 M 7122 9451 L 7122 9451 M 5446 11039 L 5446 11037 M 8125 10356 L 7923 10469 M 8287 10255 L 8125 10356 M 7923 10469 L 7785 10535 M 8651 10013 L 8478 10132 M 8821 9887 L 8651 10013 M 9365 9440 L 9190 9596 M 9397 9407 L 9365 9440 M 9190 9596 L 8982 9765 M 7785 10535 L 7643 10598 L 7548 10637 M 8478 10132 L 8287 10255 M 8982 9765 L 8821 9887 M 7548 10637 L 7126 9453 L 9370 9387 M 10014 8676 L 9858 8887 M 10179 8429 L 10014 8676 M 9858 8887 L 9689 9095 L 9533 9269 L 9397 9407 M 10288 8248 L 10179 8429 M 10393 8054 L 10288 8248 M 10541 7746 L 10488 7863 M 10585 7637 L 10541 7746 M 10488 7863 L 10470 7902 L 10393 8054 M 9370 9387 L 9376 7130 L 9377 7130 M 9376 7130 L 9356 7121 M 9397 9407 L 9370 9387 M 10982 5959 L 10958 6186 M 10995 5723 L 10982 5959 M 10916 6463 L 10876 6681 L 10828 6889 M 10694 7342 L 10629 7527 L 10585 7637 M 10958 6186 L 10916 6463 M 10828 6889 L 10794 7017 L 10744 7185 M 10998 5609 L 10995 5723 M 10998 5533 L 10998 5609 M 10744 7185 L 10694 7342 M 10585 7637 L 9377 7130 L 10978 5533 M 10702 3750 L 10771 3967 L 10823 4165 M 10980 5096 L 10996 5383 L 10998 5533 M 10957 4870 L 10980 5096 M 10617 3516 L 10702 3750 M 10567 3397 L 10617 3516 M 10919 4621 L 10957 4870 M 10864 4342 L 10919 4621 M 10823 4165 L 10864 4342 M 10998 5533 L 10978 5533 L 9336 3958 M 9998 2379 L 10124 2567 M 9866 2199 L 9998 2379 M 10242 2757 L 10383 3009 L 10468 3178 M 10124 2567 L 10242 2757 M 9482 1748 L 9630 1910 M 9411 1676 L 9482 1748 M 9630 1910 L 9767 2073 M 10468 3178 L 10542 3336 L 10567 3397 M 9767 2073 L 9866 2199 M 10567 3397 L 9335 3936 L 9402 1684 M 9334 3937 L 9335 3936 M 8956 1267 L 9159 1437 M 8757 1115 L 8956 1267 M 8423 886 L 8616 1015 M 8220 763 L 8423 886 M 7690 494 L 7924 603 M 7611 462 L 7690 494 M 7924 603 L 8087 688 M 9159 1437 L 9339 1604 L 9411 1676 M 8616 1015 L 8757 1115 M 8087 688 L 8220 763 M 7127 1729 L 7611 462 M 9402 1684 L 7127 1729 M 9411 1676 L 9402 1684 M 3894 1718 L 5456 2483 L 3285 3363 M 5499 2483 L 5456 2483 M 3283 3360 L 3894 1718 M 1654 3917 L 1654 3916 L 3283 3360 M 2410 5557 L 1654 3917 M 3283 3360 L 3285 3363 L 2411 5557 M 1641 7152 L 1634 7145 L 2410 5557 M 3367 7768 L 1641 7152 M 2410 5557 L 2411 5557 L 3368 7766 M 3947 9382 L 3367 7768 M 5501 8629 L 3947 9382 M 3367 7768 L 3368 7766 L 5501 8625 M 7122 9451 L 5501 8629 M 7660 7712 L 7122 9451 M 5501 8629 L 5501 8625 L 7654 7706 M 8557 5619 L 9356 7121 M 7654 7706 L 8557 5619 L 8557 5564 M 9356 7121 L 7660 7712 L 7654 7706 M 8557 5564 L 7719 3411 M 9336 3958 L 8557 5564 M 7719 3411 L 9334 3937 M 7719 3411 L 7699 3391 M 9334 3937 L 9336 3958 M 7699 3391 L 5499 2483 L 7104 1750 L 7699 3391" stroke-linecap="round" />
</g>
<g transform="matrix(0.8 -0.12 0.12 0.8 326.9 302.9)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 32.5 -34)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0" y1="-3" x2="0" y2="3" />
</g>
		<g transform="matrix(1 0 0 1 30.5 -22)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="2" y1="-8.5" x2="-2" y2="8.5" />
</g>
		<g transform="matrix(1 0 0 1 26.5 -5.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="2" y1="-8" x2="-2" y2="8" />
</g>
		<g transform="matrix(1 0 0 1 21 8.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="3.5" y1="-6" x2="-3.5" y2="6" />
</g>
		<g transform="matrix(1 0 0 1 13.5 19)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="4" y1="-4.5" x2="-4" y2="4.5" />
</g>
		<g transform="matrix(1 0 0 1 4.5 28.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="5" y1="-5" x2="-5" y2="5" />
</g>
		<g transform="matrix(1 0 0 1 -4.5 37)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="4" y1="-3.5" x2="-4" y2="3.5" />
</g>
		<g transform="matrix(1 0 0 1 -14 43)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="5.5" y1="-2.5" x2="-5.5" y2="2.5" />
</g>
		<g transform="matrix(1 0 0 1 -25 47)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="5.5" y1="-1.5" x2="-5.5" y2="1.5" />
</g>
		<g transform="matrix(1 0 0 1 -32 49.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="1.5" y1="-1" x2="-1.5" y2="1" />
</g>
		<g transform="matrix(1 0 0 1 -31.5 47.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-2" y1="3" x2="2" y2="-3" />
</g>
		<g transform="matrix(1 0 0 1 -27 43.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-2.5" y1="1" x2="2.5" y2="-1" />
</g>
		<g transform="matrix(1 0 0 1 -22 40.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-2.5" y1="2" x2="2.5" y2="-2" />
</g>
		<g transform="matrix(1 0 0 1 -17.5 36)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-2" y1="2.5" x2="2" y2="-2.5" />
</g>
		<g transform="matrix(1 0 0 1 -12 29.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-3.5" y1="4" x2="3.5" y2="-4" />
</g>
		<g transform="matrix(1 0 0 1 -6.5 22)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-2" y1="3.5" x2="2" y2="-3.5" />
</g>
		<g transform="matrix(1 0 0 1 -3 13)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-1.5" y1="5.5" x2="1.5" y2="-5.5" />
</g>
		<g transform="matrix(1 0 0 1 -1 3.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-0.5" y1="4" x2="0.5" y2="-4" />
</g>
		<g transform="matrix(1 0 0 1 0.5 -5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-1" y1="4.5" x2="1" y2="-4.5" />
</g>
		<g transform="matrix(1 0 0 1 3.5 -13.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-2" y1="4" x2="2" y2="-4" />
</g>
		<g transform="matrix(1 0 0 1 8 -20)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-2.5" y1="2.5" x2="2.5" y2="-2.5" />
</g>
		<g transform="matrix(1 0 0 1 14 -26)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-3.5" y1="3.5" x2="3.5" y2="-3.5" />
</g>
		<g transform="matrix(1 0 0 1 20.5 -33)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-3" y1="3.5" x2="3" y2="-3.5" />
</g>
		<g transform="matrix(1 0 0 1 25 -37)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-1.5" y1="0.5" x2="1.5" y2="-0.5" />
</g>
		<g transform="matrix(1 0 0 1 29.5 -40.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-3" y1="3" x2="3" y2="-3" />
</g>
		<g transform="matrix(1 0 0 1 33 -47)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-0.5" y1="3.5" x2="0.5" y2="-3.5" />
</g>
		<g transform="matrix(1 0 0 1 33 -43.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0.5" y1="-7" x2="-0.5" y2="7" />
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 0 0)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 0 0)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="NaN" y1="NaN" x2="NaN" y2="NaN" />
</g>
		<g transform="matrix(1 0 0 1 270.3 242)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0" y1="0" x2="0" y2="0" />
</g>
		<g transform="matrix(1 0 0 1 276.8 296.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-6" y1="-54" x2="6" y2="54" />
</g>
		<g transform="matrix(1 0 0 1 282.3 350)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0" y1="0" x2="0" y2="0" />
</g>
		<g transform="matrix(1 0 0 1 276.3 382.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="6.5" y1="-32" x2="-6.5" y2="32" />
</g>
		<g transform="matrix(1 0 0 1 269.3 414)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0" y1="0" x2="0" y2="0" />
</g>
		<g transform="matrix(1 0 0 1 0 0)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="NaN" y1="NaN" x2="NaN" y2="NaN" />
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 277.81 325.19)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 -4.02 -90.69)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-4.967803669673117" y1="0.5665039272434171" x2="4.967803669673117" y2="-0.5665039272434171" />
</g>
		<g transform="matrix(1 0 0 1 2.48 -33.69)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-6.5" y1="-57" x2="6.5" y2="57" />
</g>
		<g transform="matrix(1 0 0 1 2.48 56.81)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="6.5" y1="-33.5" x2="-6.5" y2="33.5" />
</g>
		<g transform="matrix(1 0 0 1 -4.02 90.31)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-4.908457838387108" y1="-0.952387341776614" x2="4.908457838387108" y2="0.952387341776614" />
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 268.3 305)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 0 -8.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-5" y1="0" x2="5" y2="0" />
</g>
		<g transform="matrix(1 0 0 1 0.5 -0.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0" y1="-8.5" x2="0" y2="8.5" />
</g>
		<g transform="matrix(1 0 0 1 0 8.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-5" y1="0" x2="5" y2="0" />
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 260.3 305)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 0 -24.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-5" y1="0" x2="5" y2="0" />
</g>
		<g transform="matrix(1 0 0 1 0.5 -0.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0" y1="-24.5" x2="0" y2="24.5" />
</g>
		<g transform="matrix(1 0 0 1 0 24.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-5" y1="0" x2="5" y2="0" />
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 244.82 321.32)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 -1.02 -74.82)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-4.961389383568331" y1="0.6201736729460379" x2="4.961389383568331" y2="-0.6201736729460379" />
</g>
		<g transform="matrix(1 0 0 1 2.48 -46.82)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-3.5" y1="-28" x2="3.5" y2="28" />
</g>
		<g transform="matrix(1 0 0 1 3.48 28.18)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="2.5" y1="-47" x2="-2.5" y2="47" />
</g>
		<g transform="matrix(1 0 0 1 0.98 75.18)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-4.992941637972876" y1="-0.2655820020198121" x2="4.992941637972876" y2="0.2655820020198121" />
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 104.3 395)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 -23.5 -26.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-3.7409383007832417" y1="3.317435851637981" x2="3.7409383007832417" y2="-3.317435851637981" />
</g>
		<g transform="matrix(1 0 0 1 0 0)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-23.5" y1="-26.5" x2="23.5" y2="26.5" />
</g>
		<g transform="matrix(1 0 0 1 23.5 26.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-3.740938300783249" y1="3.317435851637981" x2="3.740938300783249" y2="-3.317435851637981" />
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 48.8 248.5)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 -7 7.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0" y1="3" x2="0" y2="-3" />
</g>
		<g transform="matrix(1 0 0 1 -6 0.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-1" y1="4.5" x2="1" y2="-4.5" />
</g>
		<g transform="matrix(1 0 0 1 -3.5 -7.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-1.5" y1="3.5" x2="1.5" y2="-3.5" />
</g>
		<g transform="matrix(1 0 0 1 2 -14)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-4" y1="3" x2="4" y2="-3" />
</g>
		<g transform="matrix(1 0 0 1 6 -16)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0" y1="-1.5" x2="0" y2="1.5" />
</g>
		<g transform="matrix(1 0 0 1 6.5 -11.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-0.5" y1="-2.5" x2="0.5" y2="2.5" />
</g>
		<g transform="matrix(1 0 0 1 7.5 -6)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-0.5" y1="-3" x2="0.5" y2="3" />
</g>
		<g transform="matrix(1 0 0 1 8 0)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0" y1="-3.5" x2="0" y2="3.5" />
</g>
		<g transform="matrix(1 0 0 1 7.5 7)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0.5" y1="-3" x2="-0.5" y2="3" />
</g>
		<g transform="matrix(1 0 0 1 4.5 12)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="2.5" y1="-2" x2="-2.5" y2="2" />
</g>
		<g transform="matrix(1 0 0 1 -1 15.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="3" y1="-1.5" x2="-3" y2="1.5" />
</g>
		<g transform="matrix(1 0 0 1 -6 16)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="2" y1="1" x2="-2" y2="-1" />
</g>
		<g transform="matrix(1 0 0 1 -8 13.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0" y1="1" x2="0" y2="-1" />
</g>
		<g transform="matrix(1 0 0 1 -7.5 12)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: 3 3; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-0.5" y1="1" x2="0.5" y2="-1" />
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 48.3 203)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 -5.5 -3.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-2.6843774609657984" y1="4.218307438660531" x2="2.6843774609657984" y2="-4.218307438660531" />
</g>
		<g transform="matrix(1 0 0 1 0 0)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-5.5" y1="-3.5" x2="5.5" y2="3.5" />
</g>
		<g transform="matrix(1 0 0 1 5.5 3.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-2.6843774609657984" y1="4.218307438660531" x2="2.6843774609657984" y2="-4.218307438660531" />
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 72.8 209)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 -16 -13.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-3.2243546960489127" y1="3.821457417539449" x2="3.2243546960489127" y2="-3.821457417539449" />
</g>
		<g transform="matrix(1 0 0 1 0 0)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-16" y1="-13.5" x2="16" y2="13.5" />
</g>
		<g transform="matrix(1 0 0 1 16 13.5)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-3.2243546960489198" y1="3.821457417539449" x2="3.2243546960489198" y2="-3.821457417539449" />
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 92.8 217.5)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 -24 -20)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-3.2009219983224" y1="3.8411063979868914" x2="3.2009219983224" y2="-3.8411063979868914" />
</g>
		<g transform="matrix(1 0 0 1 0 0)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-24" y1="-20" x2="24" y2="20" />
</g>
		<g transform="matrix(1 0 0 1 24 20)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-3.2009219983224" y1="3.8411063979868914" x2="3.2009219983224" y2="-3.8411063979868914" />
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 165.63 236.98)"  >
<g style=""   >
		<g transform="matrix(1 0 0 1 -45.83 1.52)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0.8033780104085508" y1="4.935036349652563" x2="-0.8033780104085508" y2="-4.935036349652563" />
</g>
		<g transform="matrix(1 0 0 1 -24.33 -1.98)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-21.5" y1="3.5" x2="21.5" y2="-3.5" />
</g>
		<g transform="matrix(1 0 0 1 5.67 -1.98)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-8.5" y1="-3.5" x2="8.5" y2="3.5" />
</g>
		<g transform="matrix(1 0 0 1 30.17 0.02)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="-16" y1="1.5" x2="16" y2="-1.5" />
</g>
		<g transform="matrix(1 0 0 1 46.17 -1.48)"  >
<line style="stroke: rgb(255,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  x1="0.466703543465286" y1="4.978171130296431" x2="-0.466703543465286" y2="-4.978171130296431" />
</g>
</g>
</g>
<g transform="matrix(1 0 0 1 163.8 438.5)" id="Pinpoint (Pp)_1750675162537"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 138.8 424.5)" id="Pinpoint (Pp)_1750675163513"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 167.8 425.5)" id="Pinpoint (Pp)_1750675164641"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 157.8 413.5)" id="Pinpoint (Pp)_1750675165393"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 139.8 396.5)" id="Pinpoint (Pp)_1750675168393"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 152.8 388.5)" id="Pinpoint (Pp)_1750675168896"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 132.8 352.5)" id="Pinpoint (Pp)_1750675169730"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 117.8 375.5)" id="Pinpoint (Pp)_1750675170249"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 116.8 339.5)" id="Pinpoint (Pp)_1750675170825"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 61.8 337.5)" id="Pinpoint (Pp)_1750675173113"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 72.8 347.5)" id="Pinpoint (Pp)_1750675173864"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 88.8 330.5)" id="Pinpoint (Pp)_1750675174416"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 55.8 315.5)" id="Pinpoint (Pp)_1750675176440"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 70.8 309.5)" id="Pinpoint (Pp)_1750675176984"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 45.8 283.5)" id="Pinpoint (Pp)_1750675177681"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 108.8 164.5)" id="Pinpoint (Pp)_1750675186977"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 142.8 155.5)" id="Pinpoint (Pp)_1750675189097"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 159.8 119.5)" id="Pinpoint (Pp)_1750675189752"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 137.8 111.5)" id="Pinpoint (Pp)_1750675190448"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 110.8 102.5)" id="Pinpoint (Pp)_1750675191041"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 152.8 74.5)" id="Pinpoint (Pp)_1750675193392"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 238.8 103.5)" id="Pinpoint (Pp)_1750675197561"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 276.8 100.5)" id="Pinpoint (Pp)_1750675198241"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 250.8 71.5)" id="Pinpoint (Pp)_1750675199073"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 304.8 41.5)" id="Pinpoint (Pp)_1750675201585"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 349.8 78.5)" id="Pinpoint (Pp)_1750675204122"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
<g transform="matrix(1 0 0 1 334.8 110.5)" id="Pinpoint (Pp)_1750675207489"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,0,0); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="1" />
</g>
</svg>